name: <PERSON><PERSON> and Push Docker Images

on:
  workflow_dispatch:
    inputs:
      push_image:
        description: 'Push image to registry'
        required: false
        default: true
        type: boolean

env:
  REGISTRY: ghcr.io

jobs:
  build-and-push:
    runs-on: ubuntu-latest
    permissions:
      contents: read
      packages: write
    
    strategy:
      matrix:
        image:
          - name: magentic-ui-browser
            context: src/magentic_ui/docker/magentic-ui-browser-docker
            registry_name: microsoft/magentic-ui-browser
          - name: magentic-ui-python-env
            context: src/magentic_ui/docker/magentic-ui-python-env
            registry_name: microsoft/magentic-ui-python-env

    steps:
    - name: Checkout repository
      uses: actions/checkout@v4

    - name: Set up Docker Buildx
      uses: docker/setup-buildx-action@v3

    - name: Log in to Container Registry
      if: inputs.push_image
      uses: docker/login-action@v3
      with:
        registry: ${{ env.REGISTRY }}
        username: ${{ github.actor }}
        password: ${{ secrets.GITHUB_TOKEN }}

    - name: Make build script executable
      run: chmod +x ${{ matrix.image.context }}/build.sh

    - name: Build Docker image (${{ matrix.image.name }})
      if: inputs.push_image != true
      run: |
        cd ${{ matrix.image.context }}
        ./build.sh

    - name: Build and Push Docker image (${{ matrix.image.name }})
      if: inputs.push_image
      run: |
        cd ${{ matrix.image.context }}
        ./build.sh --push
